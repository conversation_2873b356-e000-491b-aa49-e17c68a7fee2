import { prefix } from '../../prefix';

export default [
  {
    path: `${prefix}/assessmentTasks`,
    name: 'AssessmentTasks',
    component: () => import('@/views/assessmentTasks/index.vue'),
    meta: { title: '考核任务中心' },
  },
  {
    path: `${prefix}/assessmentTasks/detail`,
    name: 'AssessmentDetail',
    component: () => import('@/views/assessmentTasks/detail.vue'),
    meta: { title: '考核任务详情' },
  },
  {
    path: `${prefix}/assessmentTasks/teacherCollegeDetail`,
    name: 'TeacherCollegeDetail',
    component: () => import('@/views/assessmentTasks/teacherCollegeDetail.vue'),
    meta: { title: '教师学院考核详情' },
  },
  {
    path: `${prefix}/assessmentTasks/detailView`,
    name: 'DetailView',
    component: () => import('@/views/assessmentTasks/components/DetailView.vue'),
    meta: { title: '考核详情视图' },
  },
  {
    path: `${prefix}/assessmentTasks/history`,
    name: 'AssessmentHistory',
    component: () => import('@/views/assessmentTasks/history.vue'),
    meta: { title: '历史记录' },
  },
  {
    path: `${prefix}/assessmentTasks/dataConfirm`,
    name: 'DataConfirm',
    component: () => import('@/views/assessmentTasks/DataConfirm.vue'),
    meta: { title: '数据确认' },
  },
  {
    path: `${prefix}/assessmentTasks/textbookDetail`,
    name: 'TextbookDetail',
    component: () => import('@/views/assessmentTasks/textbookDetail.vue'),
    meta: { title: '教材详情' },
  }
];
