<template>
  <div>
    <el-link type="primary" @click="$emit('detail', row)">详情</el-link>
    <el-link
      v-if="!row.confirmed"
      type="success"
      @click="handleAssign"
      style="margin-left: 8px"
      >分配</el-link
    >
    <el-link
      v-else
      type="danger"
      @click="$emit('revoke', row)"
      style="margin-left: 8px"
      >驳回</el-link
    >
    
    <!-- 分配弹窗 -->
    <allocation-dialog
      :visible="allocationDialogVisible"
      :total-amount="180"
      :current-user="currentUser"
      :available-targets="availableTargets"
      @submit="handleAllocationSubmit"
      @update:visible="allocationDialogVisible = $event"
    />
  </div>
</template>

<script>
import AllocationDialog from './AllocationDialog.vue';
import { getCurrentUser, getAvailableTargets, submitAllocation } from '../services/userService.js';

export default {
  name: 'TableActions',
  components: {
    AllocationDialog
  },
  props: {
    row: Object,
  },
  data() {
    return {
      allocationDialogVisible: false,
      currentUser: {},
      availableTargets: [],
      loading: false
    };
  },
  async created() {
    await this.loadUserData();
  },
  methods: {
    // 加载用户数据
    async loadUserData() {
      try {
        this.loading = true;
        const [userResponse, targetsResponse] = await Promise.all([
          getCurrentUser(),
          getAvailableTargets()
        ]);
        
        if (userResponse.success) {
          this.currentUser = {
            id: userResponse.data.id,
            name: userResponse.data.displayName
          };
        }
        
        if (targetsResponse.success) {
          this.availableTargets = targetsResponse.data;
        }
      } catch (error) {
        console.error('加载用户数据失败:', error);
        this.$message.error('加载用户数据失败');
      } finally {
        this.loading = false;
      }
    },
    
    // 处理分配按钮点击
    handleAssign() {
      this.allocationDialogVisible = true;
    },
    
    // 处理分配提交
    async handleAllocationSubmit(allocationData) {
      try {
        this.loading = true;
        const response = await submitAllocation({
          ...allocationData,
          taskId: this.row.id,
          taskName: this.row.name
        });
        
        if (response.success) {
          this.$message.success(response.message || '分配成功');
          this.$emit('assign', this.row);
        } else {
          this.$message.error(response.message || '分配失败');
        }
      } catch (error) {
        console.error('分配失败:', error);
        this.$message.error(error.message || '分配失败');
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>
