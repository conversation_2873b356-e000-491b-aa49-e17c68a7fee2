// 用户服务 - 模拟API调用

// 模拟当前用户数据
const currentUser = {
  id: 1,
  name: '张三',
  displayName: '张三(我)',
  department: '计算机学院',
  role: '教师'
};

// 模拟可用分配对象列表
const availableTargets = [
  { id: 1, name: '张三(我)', department: '计算机学院', role: '教师' },
  { id: 2, name: '王斐', department: '计算机学院', role: '教师' },
  { id: 3, name: '李四', department: '数学学院', role: '教师' },
  { id: 4, name: '赵五', department: '物理学院', role: '教师' },
  { id: 5, name: '孙六', department: '化学学院', role: '教师' },
  { id: 6, name: '周七', department: '生物学院', role: '教师' }
];

// 获取当前用户信息
export const getCurrentUser = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        data: currentUser
      });
    }, 100);
  });
};

// 获取可用分配对象列表
export const getAvailableTargets = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        data: availableTargets
      });
    }, 200);
  });
};

// 提交分配结果
export const submitAllocation = (allocationData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 模拟验证
      if (!allocationData || !allocationData.allocations || allocationData.allocations.length === 0) {
        reject(new Error('分配数据不能为空'));
        return;
      }
      
      // 模拟成功响应
      resolve({
        success: true,
        message: '分配成功',
        data: {
          id: Date.now(),
          ...allocationData,
          createTime: new Date().toISOString()
        }
      });
    }, 500);
  });
};

// 获取分配历史
export const getAllocationHistory = (taskId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const history = [
        {
          id: 1,
          taskId: taskId,
          method: 'proportion',
          totalAmount: 180,
          allocations: [
            { targetId: 1, targetName: '张三(我)', proportion: 90, result: 162 },
            { targetId: 2, targetName: '王斐', proportion: 10, result: 18 }
          ],
          createTime: '2024-01-15 10:30:00',
          status: 'completed'
        }
      ];
      
      resolve({
        success: true,
        data: history
      });
    }, 300);
  });
};