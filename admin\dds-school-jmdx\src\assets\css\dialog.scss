::v-deep .el-dialog {
  width: 894px;
  height: 598px;
  background: #021b19;
  box-shadow: inset 0px 1px 31px 0px rgba(68, 180, 150, 0.5);
  border: 2px solid;
  border-image: linear-gradient(
      156deg,
      rgba(20, 138, 255, 0),
      rgba(20, 138, 255, 0)
    )
    2 2;
  .el-dialog__header {
    height: 52px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    padding-left: 32px;
    padding-right: 32px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  .el-dialog__headerbtn {
    right: 32px;
  }
  .el-dialog__body {
    padding-top: 0;
    padding: 0 27px 24px;
  }
}

::v-deep .el-table {
  padding: 0 5px !important;
  background-color: transparent;
  margin: 24px 0 !important;
  .el-table__header {
    background-color: #041819 !important;
    color: #a3a6ad !important;
    border: none;
  }
  .el-table__row {
    width: 831px;
    height: 49px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.04);
  }
   th {
    background-color: rgba(255, 255, 255, 0.01) !important;
    color: #ffffff !important;
    .cell {
      opacity: 0.5;
      color: #fff !important;
      border: none;
    }
  }
  tr {
    background-color: transparent;
  }
 td {
    background: rgba(255, 255, 255, 0.04);
    border-color: rgba(255, 255, 255, 0.04) !important;
    .cell {
      color: #fff !important;
      border: none;
    }
  }
  /* 去掉上面的线 */
  th.is-leaf {
    border: none !important;
  }

  /* 去掉最下面的那一条线 */
  &::before {
    height: 0px;
  }

  tbody tr:hover > td {
    background-color: #008468 !important; //修改成自己想要的颜色即可
  }

  ::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 10px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }

  ::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    background-color: #3dbb9e;
  }

  ::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #072723;
    border-radius: 10px;
  }
}
::v-deep #project_frame .el-table {
  td,
  th {
    padding: 13px 0;

    border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
  }
}


::v-deep .el-pagination {
  text-align: right;
  .el-pagination__jump,
  .el-pagination__editor,
  .el-pagination__sizes,
  .el-pagination__total,
  .vxe-pager--prev-btn,
  .vxe-pager--next-btn,
  .vxe-pager--num-btn,
  .vxe-pager--jump-next,
  .btn-prev,
  .btn-next,
  .number,
  .btn-quicknext {
    background-color: transparent;
    color: #a3a6ad;
    border-color: #008468;
  }
  .el-pager li,
  .btn-next,
  .btn-prev {
    border: 1px solid #008468 !important;
  }
  .el-pager {
    li.active {
      background-color: #008468 !important;
      color: #ffffff;
    }
  }
  button:disabled {
    color: #ffffff;
    background-color: transparent;
    cursor: not-allowed;
  }
  .el-input,
  .el-input__inner {
    background-color: transparent;
    color: #ffffff;
  }

  .el-input__inner {
    border: 1px solid #008468 !important;
  }
}

.light {
  ::v-deep .el-dialog {
    background-color: #e5f5f1;
    box-shadow: inset 0px 1px 31px 0px rgba(68, 180, 150, 0.5);
    border: 2px solid;
    border-image: linear-gradient(
        156deg,
        rgba(20, 138, 255, 0),
        rgba(20, 138, 255, 0)
      )
      2 2;
    .el-dialog__header {
      color: #222;
      border-bottom: 1px solid rgba(8, 83, 75, 0.16);
    }
  }
  ::v-deep .el-table {
    .el-table__header {
      background-color: rgba(0, 179, 140, 0.1) !important;
      color: #2b4051 !important;
    }
    .el-table__row {
      color: #222;
      background: #f5f5f5;
    }
    th {
      background-color: transparent;
      color: #222 !important;
      .cell {
        opacity: 1;
        color: #222 !important;
      }
    }
    td {
      background: #f5f5f5;
      border-color: rgba(8, 83, 75, 0.16) !important;
      .cell {
        color: #222 !important;
        border: none;
      }
    }
 
    tbody tr:hover > td {
      background-color: rgba(
        0,
        132,
        104,
        0.16
      ) !important; //修改成自己想要的颜色即可
    }

    ::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      background-color: #c4e8e5;
    }

    ::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      box-shadow: none;
      background: #f5f5f5;
    }
  }
  ::v-deep #project_frame .el-table {
    td,
    th {
      border-bottom: 1px solid rgba(8, 83, 75, 0.16) !important;
    }
  }

  ::v-deep .el-pagination {
    .el-pagination__jump,
    .el-pagination__editor,
    .el-pagination__sizes,
    .el-pagination__total,
    .vxe-pager--prev-btn,
    .vxe-pager--next-btn,
    .vxe-pager--num-btn,
    .vxe-pager--jump-next,
    .btn-prev,
    .btn-next,
    .number,
    .btn-quicknext {
      color: #222;
      border-color: #00b38c;
    }
    .el-pager li,
    .btn-next,
    .btn-prev {
      color: #00b38c;
      border: 1px solid #00b38c !important;
    }
    .el-pager {
      li.active {
        background-color: #00b38c !important;
        color: #ffffff;
      }
    }
    button:disabled {
      background-color: transparent;
      cursor: not-allowed;
      opacity: 0.4;
    }
    .el-input,
    .el-input__inner {
      background-color: transparent;
      color: #222;
    }
    .el-input__inner {
      border: 1px solid #00b38c !important;
    }
  }
}
.blue {
  ::v-deep .el-dialog {
    box-shadow: inset 0px 0px 16px 0px rgba(95,214,246,0.5);
    background: linear-gradient( 180deg, #042C60 0%, #03224A 100%);
    border-radius: 4px;
    border: 2px solid;
    border-image: linear-gradient(180deg, rgba(95, 214, 246, 0.2), rgba(95, 214, 246, 0.9), rgba(95, 214, 246, 0.2)) 2 2;
    .el-dialog__header {
      color: #FFFFFF;
      border-bottom: 1px solid  rgba(255,255,255,0.1);;
    }
  }
  ::v-deep .el-table {
    .el-table__header {
      color: #A2CFF4 !important;
      background: rgba(95,214,246,0.1) !important;
    }
    .el-table__row {
      color: #fff;
      background: rgba(255,255,255,0.04);
    }
    th {
      background-color: transparent;
      color: #fff !important;
      .cell {
        opacity: 1;
        color: #fff !important;
      }
    }
    td {
      background: #003160;
      border-color: rgba(255,255,255,0.06) !important;
      .cell {
        color: #fff !important;
        border: none;
      }
    }
 
    tbody tr:hover > td {
      background-color: rgba(
        255,
        255,
        255,
        0.1
      ) !important; //修改成自己想要的颜色即可
    }

    ::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      background-color: #03457F;
    }

    ::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      box-shadow: none;
      background: #f5f5f5;
    }
  }
  ::v-deep #project_frame .el-table {
    td,
    th {
      border-bottom: 1px solid rgba(255,255,255,0.06)!important;
    }
  }

  ::v-deep .el-pagination {
    .el-pagination__jump,
    .el-pagination__editor,
    .el-pagination__sizes,
    .el-pagination__total,
    .vxe-pager--prev-btn,
    .vxe-pager--next-btn,
    .vxe-pager--num-btn,
    .vxe-pager--jump-next,
    .btn-prev,
    .btn-next,
    .number,
    .btn-quicknext {
      color: #fff;
      border-color: #53A5DC;
    }
    .el-pager li,
    .btn-next,
    .btn-prev {
      color: #53A5DC;
      border: 1px solid #53A5DC !important;
    }
    .el-pager {
      li.active {
        background-color: #53A5DC !important;
        color: #ffffff;
      }
    }
    button:disabled {
      background-color: transparent;
      cursor: not-allowed;
      opacity: 0.4;
    }
    .el-input,
    .el-input__inner {
      background-color: transparent;
      color: #fff;
    }
    .el-input__inner {
      border: 1px solid #53A5DC !important;
    }
  }
}
