const path = require('path');
const CompressionPlugin = require('compression-webpack-plugin');
function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = {
  chainWebpack: (config) => {
    config.output.filename('js/[name].[hash:8].js').end();
    config.module
      .rule('images')
      .use('url-loader')
      .tap((options) => {
        options.fallback.options.name = 'img/[name].[hash:8].[ext]';
        return options;
      });
    config.plugins.delete('preload'); // TODO: need test
    config.plugins.delete('prefetch'); // TODO: need test

    // =set svg-sprite-loader
    config.module.rule('svg').exclude.add(resolve('src/assets/icons')).end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
      })
      .end();

    config.devServer.set('inline', false);
    config.devServer.set('hot', true);
    config.output.filename(`js/[name].js`);
    // set dependents ignore
    config.externals([
      'vue',
      'vue-router',
      'vuex',
      'axios',
      'element-ui',
      'vue-i18n',
      'js-cookie',
    ]);
  },
  configureWebpack: {
    plugins: [
      new CompressionPlugin({
        cache: false,
        algorithm: 'gzip', // 使用gzip压缩
        test: /\.js$|\.html$|\.css$/, // 匹配文件名
        filename: '[path][base].gz[query]', // 压缩后的文件名(保持原文件名，后缀加.gz)
        minRatio: 1, // 压缩率小于1才会压缩
        threshold: 10240, // 对超过10k的数据压缩
        deleteOriginalAssets: false, // 是否删除未压缩的源文件，谨慎设置，如果希望提供非gzip的资源，可不设置或者设置为false（比如删除打包后的gz后还可以加载到原始资源文件）
      }),
    ],
    output: {
      // library的值在所有子应用中需要唯一
      library: 'fjnz',
      libraryTarget: 'umd',
    },
  },
  filenameHashing: false,
  productionSourceMap: false,
};
