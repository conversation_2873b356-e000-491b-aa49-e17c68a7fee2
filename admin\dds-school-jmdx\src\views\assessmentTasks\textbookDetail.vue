<template>
  <DT-View>
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">{{ textbookInfo.title }}</h1>
      <div class="page-time">
        （数据确认时间：{{ textbookInfo.confirmTime }}）
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-overview">
      <!-- 分类标签和导入按钮 -->
      <div class="stats-header">
        <div class="title-section">
          <div class="title-content">
            <span
              class="title-text"
              :class="{ 'title-collapsed': !titleExpanded }"
            >
              {{ textbookInfo.category }}
            </span>
            <el-button
              type="text"
              size="mini"
              class="expand-btn"
              @click="toggleTitle"
            >
              {{ titleExpanded ? '收起' : '展开' }}
              <i
                :class="
                  titleExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
                "
              ></i>
            </el-button>
          </div>
          <span class="description-text">{{
            textbookInfo.categoryDescription
          }}</span>
          <el-button type="primary" size="small" class="confirm-btn"
            >确认无误</el-button
          >
        </div>
      </div>

      <!-- 指标得分和分解指标区域 -->
      <div class="score-breakdown-section">
        <!-- 左侧：指标得分 -->
        <div class="score-main">
          <div class="score-label"> 指标得分 </div>
          <div class="score-value">{{ totalScore.toLocaleString() }}</div>
          <el-tooltip
            class="item"
            effect="dark"
            content="Top Center 提示文字"
            placement="top"
          >
            <i class="el-icon-info score-info"></i>
          </el-tooltip>
        </div>

        <!-- 右侧：分解指标 -->
        <div class="breakdown-section">
          <div class="breakdown-label">分解指标</div>
          <div class="breakdown-grid">
            <div
              class="breakdown-item"
              v-for="item in scoreBreakdown"
              :key="item.label"
            >
              <div class="breakdown-title">{{ item.label }}</div>
              <div class="breakdown-value">{{ item.value }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据说明 -->
      <div class="data-description">
        <div class="description-row">
          <span class="desc-label">数据说明</span>
          <span class="desc-value">{{ textbookInfo.dataDescription }}</span>
        </div>
        <div class="description-row">
          <span class="desc-label">仅显示有效数据</span>
          <el-button type="primary" size="small" class="update-btn"
            >更新</el-button
          >
        </div>
      </div>
    </div>

    <!-- 操作按钮区 -->
    <div class="action-buttons">
      <el-button class="action-btn">去填报</el-button>
      <el-button class="action-btn">查看填报记录</el-button>
      <el-button class="action-btn">查看指标说明</el-button>
      <el-dropdown class="dropdown-btn">
        <el-button type="primary" class="action-btn">
          高级查询 <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>选项1</el-dropdown-item>
          <el-dropdown-item>选项2</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 教材列表 -->
    <div class="textbook-list">
      <el-table
        :data="textbookData"
        style="width: 100%"
        border
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#333',
          fontWeight: '600',
        }"
      >
        <el-table-column prop="name" label="教材名称" min-width="200" />
        <el-table-column prop="subject" label="主要编者" width="120" />
        <el-table-column prop="publisher" label="报送单位" width="120" />
        <el-table-column prop="publishingHouse" label="出版单位" width="120" />
        <el-table-column prop="usageRange" label="使用范围" width="120" />
        <el-table-column prop="category" label="获奖级别" width="120" />
        <el-table-column prop="level" label="获奖等级" width="120" />
        <el-table-column prop="score" label="得分" width="100" align="center" />
        <el-table-column prop="status" label="合格指标规则" width="150" />
        <el-table-column prop="situation" label="其他情况" width="120" />
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewDetails(scope.row)"
              >详情</el-button
            >
            <el-button type="text" size="small" @click="editItem(scope.row)"
              >分析</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </DT-View>
</template>

<script>
export default {
  name: 'TextbookDetail',
  data() {
    return {
      titleExpanded: false, // 标题展开状态
      textbookInfo: {
        title: '2024年度积分方案',
        confirmTime: '2022-12-02 至 2021-12-10',
        category: '国家级教学成果奖特等奖/一等奖/二等奖',
        categoryDescription:
          '请分数据确认基础数据统计情况，数据填报，请联系了7月数据期间，联系相关人员',
        dataDescription:
          '数据确认基础数据统计情况，数据填报，请联系了7月数据期间，联系相关人员',
      },
      totalScore: 10085,
      scoreBreakdown: [
        { label: '国家级教材建设奖特等奖', value: 0 },
        { label: '国家级教材建设奖一等奖', value: 9935 },
        { label: '国家级教材建设奖二等奖', value: 0 },
        { label: '省级教材建设奖特等奖', value: 0 },
        { label: '省级教材建设奖一等奖', value: 0 },
        { label: '省级教材建设奖二等奖', value: 150 },
      ],
      textbookData: [
        {
          id: 1,
          name: '人工智能入门精讲',
          subject: '李明',
          publisher: '集美大学',
          publishingHouse: '第一出版社',
          usageRange: '本科生',
          category: '国家级',
          level: '一等奖',
          score: 4950,
          status: '国家级教材建设奖一等奖',
          situation: '国家大学出版社',
        },
        {
          id: 2,
          name: '人工智能入门精讲2',
          subject: '李明',
          publisher: '集美大学',
          publishingHouse: '第一出版社',
          usageRange: '本科生',
          category: '普级',
          level: '二等奖',
          score: 150,
          status: '省级教材建设奖二等奖',
          situation: '国家大学出版社',
        },
        {
          id: 3,
          name: '人工智能入门精讲3',
          subject: '李明',
          publisher: '集美大学',
          publishingHouse: '第一出版社',
          usageRange: '本科生',
          category: '国家级',
          level: '一等奖',
          score: 4985,
          status: '国家级教材建设奖一等奖',
          situation: '国家大学出版社',
        },
        {
          id: 4,
          name: '人工智能入门精讲4',
          subject: '李明明',
          publisher: '厦门大学',
          publishingHouse: '第一出版社',
          usageRange: '本科生',
          category: '校级',
          level: '一等奖',
          score: 0,
          status: '无',
          situation: '国家大学出版社',
        },
        {
          id: 5,
          name: '人工智能入门精讲5',
          subject: '李明',
          publisher: '集美大学',
          publishingHouse: '第一出版社',
          usageRange: '本科生',
          category: '校级',
          level: '一等奖',
          score: 0,
          status: '无',
          situation: '',
        },
      ],
    };
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    toggleTitle() {
      this.titleExpanded = !this.titleExpanded;
    },
    viewDetails(row) {
      console.log('查看详情:', row);
      // 可以在这里添加查看详情的逻辑
    },
    editItem(row) {
      console.log('编辑项目:', row);
      // 可以在这里添加编辑的逻辑
    },
  },
  created() {
    // 从路由参数获取相关信息
    const id = this.$route.params.id;
    const name = this.$route.query.name;

    if (name) {
      this.textbookInfo.title = name;
    }

    console.log('教材详情ID:', id);
  },
};
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 24px;
  display: flex;
  align-items: center;

  .page-title {
    height: 20px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #060607;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }

  .page-time {
    height: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #979da6;
    line-height: 14px;
    text-align: left;
    font-style: normal;
  }
}

.statistics-overview {
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 16px;

  .title-section {
    flex: 1;
    margin-right: 16px;
    display: flex;
    align-items: center;

    .title-content {
      display: flex;
      align-items: center;
      width: 288px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #060607;
      line-height: 16px;
      text-align: left;
      font-style: normal;

      .title-text {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #060607;
        line-height: 1.5;
        text-align: left;
        font-style: normal;

        word-break: break-all;
        transition: all 0.3s ease;

        &.title-collapsed {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 200px;
        }
      }

      .expand-btn {
        padding: 0;
        margin: 0 4px;
        color: #409eff;
        font-size: 12px;

        &:hover {
          color: #66b1ff;
        }
      }

      .source-tag {
        font-size: 12px;
        height: 20px;
        line-height: 18px;
        padding: 0 6px;
        border-radius: 2px;
      }

      .description-text {
        font-size: 14px;
        color: #86909c;
        max-width: 400px;
        word-break: break-all;
      }
    }
  }

  .confirm-btn {
    font-size: 14px;
    height: 32px;
    padding: 0 16px;
    white-space: nowrap;
    margin-left: auto;
  }

  .score-breakdown-section {
    margin-bottom: 20px;
    margin-top: 16px;
    width: 100%;
    min-height: 152px;
    background: rgba(22, 119, 255, 0.06);
    border-radius: 4px;
    padding: 20px;
    box-sizing: border-box;

    .score-main {
      display: flex;
      align-items: center;
      height: 20px;

      .score-label {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #060607;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        min-width: 90px;
      }

      .score-info {
        margin-left: 4px;
        color: #c9cdd4;
        cursor: pointer;
      }
      .score-value {
        font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
        font-weight: 500;
        font-size: 20px;
        color: #060607;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
    }

    .breakdown-section {
      display: flex;
      background: #FFFFFF;
border-radius: 4px;
margin-top: 20px;
      .breakdown-label {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #060607;
        text-align: left;
        font-style: normal;
        min-width: 90px;

      }

      .breakdown-grid {
        flex: 1;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 20px;

        .breakdown-item {
          text-align: left;

          .breakdown-title {
            height: 12px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #2f3338;
            line-height: 12px;
            text-align: left;
            font-style: normal;
          }

          .breakdown-value {
            height: 16px;
            font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
            font-weight: 500;
            font-size: 16px;
            color: #060607;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            margin-top: 12px;
          }
        }
      }
    }
  }

  .data-description {
    border-top: 1px solid #e5e6eb;
    padding-top: 16px;

    .description-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .desc-label {
        font-size: 14px;
        color: #86909c;
        margin-right: 16px;
        white-space: nowrap;
      }

      .desc-value {
        font-size: 14px;
        color: #4e5969;
        flex: 1;
      }

      .update-btn {
        font-size: 14px;
        height: 28px;
        padding: 0 12px;
      }
    }
  }
}

.action-buttons {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;

  .action-btn {
    height: 32px;
    padding: 0 16px;
    font-size: 14px;
    border-radius: 4px;
  }

  .dropdown-btn {
    .action-btn {
      display: flex;
      align-items: center;
    }
  }
}

.textbook-list {
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

// 响应式设计
@media (max-width: 768px) {
  .textbook-detail-container {
    padding: 16px;
  }

  .statistics-overview {
    padding: 16px;

    .stats-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .title-section {
        margin-right: 0;

        .title-content {
          .description-text {
            max-width: 100%;
          }
        }
      }
    }

    .score-breakdown-section {
      flex-direction: column;
      gap: 24px;

      .score-main {
        min-width: auto;
      }

      .breakdown-section {
        .breakdown-grid {
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 16px;

          .breakdown-item {
            .breakdown-value {
              font-size: 16px;
            }
          }
        }
      }
    }
  }

  .action-buttons {
    flex-wrap: wrap;
  }
}
</style>
