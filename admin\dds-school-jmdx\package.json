{"name": "dds_jmdx", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --port 13644", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@smallwei/avue": "^2.10.14", "axios": "0.19.2", "babel-plugin-transform-remove-console": "^6.9.4", "core-js": "^3.20.3", "crypto-js": "^4.2.0", "dayjs": "^1.9.6", "element-resize-detector": "^1.2.4", "element-ui": "2.13.0", "file-saver": "^2.0.5", "js-cookie": "2.2.1", "lodash": "^4.17.21", "mockjs": "^1.1.0", "monaco-editor": "^0.29.1", "monaco-editor-webpack-plugin": "^5.0.0", "shepherd.js": "^14.5.1", "single-spa-vue": "1.8.2", "systemjs-webpack-interop": "2.0.0", "tiny-pinyin": "^1.3.2", "vue": "2.6.11", "vue-count-to": "^1.0.13", "vue-i18n": "8.16.0", "vue-router": "3.1.5", "vue-seamless-scroll": "^1.1.23", "vuex": "3.1.2", "vxe-table": "^3.9.4", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/eslint-parser": "^7.19.1", "@vue/cli-plugin-babel": "~4.2.0", "@vue/cli-plugin-eslint": "~4.5.19", "@vue/cli-plugin-router": "~4.2.0", "@vue/cli-plugin-vuex": "~4.2.0", "@vue/cli-service": "~4.2.0", "babel-eslint": "^10.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^5.0.1", "eslint": "^7.32.0", "eslint-config-prettier": "^7.0.0", "eslint-loader": "^4.0.2", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "node-sass": "^4.14.1", "prettier": "^2.6.2", "sass-loader": "8.0.2", "svg-sprite-loader": "4.2.2", "vue-cli-plugin-single-spa": "1.1.0", "vue-template-compiler": "2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}}