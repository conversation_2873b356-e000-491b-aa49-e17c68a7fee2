<template>
  <div class="message-list-container">
    <!-- 消息提醒标题和数量 -->

    <div class="message-tabs">
      <span
        v-for="tab in tabs"
        :key="tab.key"
        :class="['tab-item', { active: activeTab === tab.key }]"
        @click="handleTabChange(tab.key)"
      >
        {{ tab.label }}
        <span v-if="tab.count > 0" class="tab-count">{{ tab.count }}</span>
      </span>
    </div>

    <!-- 筛选工具栏 -->
    <div class="message-toolbar" v-if="activeTab === 'notice'">
      <el-radio-group
        v-model="filterRead"
        size="small"
        class="read-filter-group"
        @change="handleReadChange"
      >
        <el-radio-button label="all">全部</el-radio-button>
        <el-radio-button label="unread">未读</el-radio-button>
        <el-radio-button label="read">已读</el-radio-button>
      </el-radio-group>

      <div class="toolbar-right">
        <el-select
          v-model="filterType"
          placeholder="请选择消息类型"
          size="small"
          style="width: 180px; margin-right: 12px"
          @change="handleTypeChange"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="type in messageTypes"
            :key="type"
            :label="type"
            :value="type"
          />
        </el-select>

        <el-input
          v-model="searchKeyword"
          placeholder="请输入关键字"
          size="small"
          style="width: 200px"
          prefix-icon="el-icon-search"
          @input="handleSearch"
          clearable
        />
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="message-list" :style="{ height: listHeight }">
      <div v-if="loading" class="loading-container">
        <el-loading :loading="true" text="加载中..." />
      </div>

      <div v-else-if="filteredMessages.length === 0" class="empty-container">
        <div class="empty-text">暂无消息</div>
      </div>

      <div v-else class="message-items">
        <div
          v-for="message in filteredMessages"
          :key="message.id"
          :class="['message-item', { unread: !message.isRead }]"
          @click="handleMessageClick(message)"
        >
          <!-- 消息状态指示器 -->
          <div class="message-status">
            <div :class="['status-dot', message.type]"></div>
            <span :class="['status-tag', message.type]">{{
              message.type
            }}</span>
          </div>

          <!-- 消息内容 -->
          <div class="message-content">
            {{ message.title }}
          </div>

          <!-- 消息时间 -->
          <div class="message-time">{{ message.time }}</div>

          <!-- 操作按钮 -->
          <div class="message-actions">
            <el-button
              type="text"
              @click.stop="handleView(message)"
              class="action-btn"
            >
              查看详情
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <!-- <div
      v-if="showPagination && filteredMessages.length > 0"
      class="message-pagination"
    >
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="totalCount"
        layout="prev, pager, next, jumper"
        @current-change="handlePageChange"
        small
      />
    </div> -->
  </div>
</template>

<script>
export default {
  name: 'MessageList',
  props: {
    // 消息数据
    messages: {
      type: Array,
      default: () => [],
    },
    // 列表高度
    listHeight: {
      type: String,
      default: 'calc(100vh - 556px)',
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true,
    },
    // 每页显示数量
    pageSize: {
      type: Number,
      default: 10,
    },
    // 是否加载中
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeTab: 'notice',
      filterRead: 'all',
      filterType: '',
      searchKeyword: '',
      currentPage: 1,
      tabs: [
        { key: 'notice', label: '消息提醒', count: 15 },
        { key: 'todo', label: '我的待办', count: 3 },
        { key: 'announce', label: '通知公告', count: 1 },
      ],
    };
  },
  computed: {
    // 未读消息数量
    unreadCount() {
      return this.messages.filter((msg) => !msg.isRead).length;
    },

    // 消息类型列表
    messageTypes() {
      const types = [...new Set(this.messages.map((msg) => msg.type))];
      return types.filter((type) => type);
    },

    // 过滤后的消息列表
    filteredMessages() {
      let filtered = [...this.messages];

      // 按读取状态过滤
      if (this.filterRead === 'unread') {
        filtered = filtered.filter((msg) => !msg.isRead);
      } else if (this.filterRead === 'read') {
        filtered = filtered.filter((msg) => msg.isRead);
      }

      // 按消息类型过滤
      if (this.filterType) {
        filtered = filtered.filter((msg) => msg.type === this.filterType);
      }

      // 按关键字搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        filtered = filtered.filter(
          (msg) =>
            msg.title.toLowerCase().includes(keyword) ||
            msg.content.toLowerCase().includes(keyword)
        );
      }

      // 分页处理
      if (this.showPagination) {
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        return filtered.slice(start, end);
      }

      return filtered;
    },

    // 总数量（用于分页）
    totalCount() {
      let filtered = [...this.messages];

      if (this.filterRead === 'unread') {
        filtered = filtered.filter((msg) => !msg.isRead);
      } else if (this.filterRead === 'read') {
        filtered = filtered.filter((msg) => msg.isRead);
      }

      if (this.filterType) {
        filtered = filtered.filter((msg) => msg.type === this.filterType);
      }

      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        filtered = filtered.filter(
          (msg) =>
            msg.title.toLowerCase().includes(keyword) ||
            msg.content.toLowerCase().includes(keyword)
        );
      }

      return filtered.length;
    },
  },
  methods: {
    // 标签页切换
    handleTabChange(tabKey) {
      this.activeTab = tabKey;
      this.$emit('tabChange', tabKey);
    },

    // 读取状态筛选
    handleReadChange(value) {
      this.currentPage = 1;
      this.$emit('filterChange', { type: 'read', value });
    },

    // 消息类型筛选
    handleTypeChange(value) {
      this.currentPage = 1;
      this.$emit('filterChange', { type: 'messageType', value });
    },

    // 搜索
    handleSearch(value) {
      this.currentPage = 1;
      this.$emit('search', value);
    },

    // 分页
    handlePageChange(page) {
      this.currentPage = page;
      this.$emit('pageChange', page);
    },

    // 消息点击
    handleMessageClick(message) {
      this.$emit('messageClick', message);
    },

    // 查看详情
    handleView(message) {
      this.$emit('viewDetail', message);
    },
  },
};
</script>

<style lang="scss" scoped>
.message-list-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  padding: 20px 24px;
  box-sizing: border-box;
  margin-top: 18px;
}

// 消息头部
.message-tabs {
  display: flex;
  gap: 24px;
  margin-bottom: 30px;

  .tab-item {
    position: relative;
    font-size: 14px;
    color: #979da6;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    transition: color 0.3s ease;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #979da6;

    &:hover {
      color: #060607;
    }

    &.active {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #060607;
      &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translate(-50%);
        width: 32px;
        height: 2px;
        background: #1677ff;
        border-radius: 2px;
      }
    }

    .tab-count {
      position: absolute;
      right: -8px;
      top: -10px;
      background: #ff4d4f;
      font-size: 12px;
      padding: 1px 5px;
      border-radius: 8px;
      margin-left: 6px;
      min-width: 16px;
      text-align: center;
      height: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      line-height: 12px;
      text-align: right;
      font-style: normal;
    }
  }
}

// 工具栏
.message-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .read-filter-group {
    margin-right: 16px;
  }

  .toolbar-right {
    display: flex;
    align-items: center;
  }
}

// 消息列表
.message-list {
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
  }
}

// 加载和空状态
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  .empty-text {
    color: #979da6;
    font-size: 14px;
  }
}

// 消息项
.message-items {
  .message-item {
    display: flex;
    align-items: center;
    height: 36px;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    transition: background-color 0.3s ease;

    // &:hover {
    //   background: #f5f7fa;
    // }

    // &:last-child {
    //   border-bottom: none;
    // }
  }
}

// 消息状态
.message-status {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  .status-dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    margin-right: 8px;
    background: #ff4d4f;
  }

  .status-tag {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
    background: rgba(250, 173, 20, 0.1);
    color: #faad14;

    &.审核 {
      background: rgba(22, 119, 255, 0.1);
      color: #1677ff;
    }
  }
}

// 消息内容
.message-content {
  flex: 1;
  min-width: 0;
  padding: 0 16px;

  height: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #060607;
  line-height: 12px;
  text-align: left;
  font-style: normal;
}

// 消息时间
.message-time {
  width: 140px;
  text-align: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #979da6;
  line-height: 12px;
  text-align: center;
  font-style: normal;
  flex-shrink: 0;
}

// 消息操作
.message-actions {
  width: 100px;
  text-align: center;
  flex-shrink: 0;

  .action-btn {
    height: 12px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #1677ff;
    line-height: 12px;
    text-align: center;
    font-style: normal;
    padding: 4px 8px;

    &:hover {
      color: #337ecc;
    }
  }
}

// 分页
.message-pagination {
  padding: 16px 24px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: center;
}
</style>
