<template>
  <div class="section-title">
    <div class="item-line"></div>
    <span>{{ title }}</span>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '基本信息',
    },
  },
  components: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  methods: {},
  created() {},
  mounted() {},
};
</script>

<style scoped>
.section-title {
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.section-title .item-line {
  margin-right: 8px;
  width: 4px;
  height: 20px;
  background: linear-gradient(180deg, #00a0fe, #0085d9);
}
</style>
