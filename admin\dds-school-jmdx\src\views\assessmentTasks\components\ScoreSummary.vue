<template>
  <div class="score-summary" style="margin-bottom: 16px">
    <div style="font-size: 20px; font-weight: bold">
      指标得分：{{ score }}
      <el-tooltip content="得分为生效数据累计得分" placement="top">
        <i class="el-icon-question"></i>
      </el-tooltip>
    </div>
    <div style="margin: 8px 0">
      <span
        v-for="item in indicators"
        :key="item.name"
        style="margin-right: 24px"
      >
        {{ item.name }}：{{ item.value }}
      </span>
    </div>
    <el-alert
      v-if="!confirmed"
      title="得分为非生效数据累计得分，请核实下方数据明细，数据有误请联系相关部门"
      type="warning"
      show-icon
      :closable="false"
      style="margin-bottom: 8px"
    />
  </div>
</template>

<script>
export default {
  name: 'ScoreSummary',
  props: {
    score: Number,
    indicators: Array,
    confirmed: <PERSON><PERSON><PERSON>,
  },
};
</script>
