<template>
  <div class="custom-switch" @click="toggle">
    <div :class="['switch-core', { 'is-checked': value }]">
      <span class="switch-label">{{ value ? '显示' : '隐藏' }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomSwitch',
  props: {
    value: Boolean,
  },
  methods: {
    toggle() {
      this.$emit('input', !this.value);
    },
  },
};
</script>

<style scoped>
.custom-switch {
  display: inline-block;
  cursor: pointer;
}
.switch-core {
  width: 60px;
  height: 28px;
  background: #dcdfe6;
  border-radius: 14px;
  position: relative;
  transition: background 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.switch-core.is-checked {
  background: #409eff;
}
.switch-label {
  color: #fff;
  font-size: 14px;
  font-weight: bold;
}
</style>
