<template>
  <div
    ref="codeContainer"
    class="editor-container"
    @mouseup="$emit('mouseup')"
    :style="{ height: height }"
  />
</template>

<script>
import * as monaco from 'monaco-editor';

/**
 * VS Code 编辑器
 *
 * 通过 getEditorVal 函数向外传递编辑器即时内容
 * 通过 initValue 用于初始化编辑器内容。
 * 编辑器默认 sql 语言，支持的语言请参考 node_modules\monaco-editor\esm\vs\basic-languages 目录下~
 * 编辑器样式仅有   'vs', 'vs-dark', 'hc-black' 三种
 */
import { language } from 'monaco-editor/esm/vs/basic-languages/sql/sql.js';
import { language as PythonLanguage } from 'monaco-editor/esm/vs/basic-languages/python/python.js';

const { keywords } = language;
export default {
  name: 'monaco-editor',
  props: {
    initValue: {
      type: String,
      default: '',
    },
    readOnly: Boolean,
    language: {
      type: String,
      default: 'sql',
    },
    height: {
      type: [Number, String],
      default: '300px',
    },
    theme: {
      type: String,
      default: 'vs-dark',
    },
    hintData: {
      type: Array,
      default: () => [],
    },
    readonlyLines: {
      type: Array,
      default: () => [],
    },
    readonlyLineContents: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      monacoEditor: null, // 语言编辑器,
      completeProvider: null,
      lastValue: '', // 保存上一次内容
      isRestoring: false, // 防止递归
      currentReadonlyLines: [], // 动态只读行
      isOnReadonlyLine: false,
      currentReadonlyLineContent: '',
    };
  },
  computed: {},
  watch: {
    theme() {
      this.setTheme(this.theme);
    },
    height() {
      this.layout();
    },
    initValue: {
      handler() {
        this.setInitValue();
      },
      deep: true,
    },
  },
  mounted() {
    this.initEditor();

    // this.completeProvider = monaco.languages.registerCompletionItemProvider(
    //   "sql",
    //   {}
    // )
    // resize
    window.addEventListener('resize', this.resize);
  },

  methods: {
    isLineReadonly(lineContent) {
      const readonlyContents = this.readonlyLineContents || [];
      return readonlyContents.some(
        (content) => lineContent.trim() === content.trim()
      );
    },
    initEditor() {
      if (this.$refs.codeContainer) {
        this.registerCompletion();
        // 初始化编辑器，确保dom已经渲染
        this.monacoEditor = monaco.editor.create(this.$refs.codeContainer, {
          value: '', // 编辑器初始显示文字
          language: this.language, // 语言
          readOnly: this.readOnly, // 是否只读 Defaults to false | true
          automaticLayout: true, // 自动布局
          theme: this.theme, // 官方自带三种主题vs, hc-black, or vs-dark
          minimap: {
            // 关闭小地图
            enabled: false,
          },
          tabSize: 2, // tab缩进长度
        });
      }
      this.setInitValue();
      this.lastValue = this.initValue;
      this.updateReadonlyLines();
      this.updateReadonlyDecorations();

      // 监听光标变化
      this.monacoEditor.onDidChangeCursorPosition((e) => {
        const model = this.monacoEditor.getModel();
        const lineNumber = e.position.lineNumber;
        const lineContent = model.getLineContent(lineNumber).trim();
        this.isOnReadonlyLine = this.isLineReadonly(lineContent);
        this.currentReadonlyLineContent = lineContent;
      });

      // 只在光标在只读行时阻止编辑
      this.monacoEditor.onDidChangeModelContent(() => {
        if (this.isRestoring) {
          this.isRestoring = false;
          return;
        }
        if (this.isOnReadonlyLine) {
          this.isRestoring = true;
          this.monacoEditor.setValue(this.lastValue);
          this.$emit('readonlyLineEdit');
        } else {
          this.lastValue = this.monacoEditor.getValue();
        }
      });
    },
    focus() {
      this.monacoEditor.focus();
    },
    layout() {
      this.monacoEditor.layout();
    },
    getValue() {
      return this.monacoEditor.getValue();
    },
    // 将 initValue Property 同步到编辑器中
    setInitValue() {
      this.monacoEditor.setValue(this.initValue);
    },
    resize() {
      this.monacoEditor.layout();
    },
    setTheme() {
      monaco.editor.setTheme(this.theme);
    },
    getSelectionVal() {
      const selection = this.monacoEditor.getSelection(); // 获取光标选中的值
      const { startLineNumber, endLineNumber, startColumn, endColumn } =
        selection;
      const model = this.monacoEditor.getModel();

      return model.getValueInRange({
        startLineNumber,
        startColumn,
        endLineNumber,
        endColumn,
      });
    },
    setPosition(column, lineNumber) {
      this.monacoEditor.setPosition({ column, lineNumber });
    },
    getPosition() {
      return this.monacoEditor.getPosition();
    },
    registerCompletion() {
      const _that = this;
      monaco.languages.registerCompletionItemProvider('sql', {
        triggerCharacters: ['.', ...keywords],
        provideCompletionItems: () => {
          let suggestions = [];
          suggestions = [..._that.getDBSuggest(), ..._that.getSQLSuggest()];
          return {
            suggestions,
          };
        },
      });
      monaco.languages.registerCompletionItemProvider('python', {
        triggerCharacters: ['.', ...PythonLanguage.keywords],
        provideCompletionItems: () => {
          let suggestions = [];
          suggestions = [..._that.getPythonSuggest()];
          return {
            suggestions,
          };
        },
      });
    },
    // 获取 SQL 语法提示
    getSQLSuggest() {
      return keywords.map((key) => ({
        label: key,
        kind: monaco.languages.CompletionItemKind.Enum,
        insertText: key,
      }));
    },
    // 获取 python语法提示
    getPythonSuggest() {
      return PythonLanguage.keywords.map((key) => ({
        label: key,
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: key,
      }));
    },

    // 自定义提示
    getDBSuggest() {
      return this.hintData.map((key) => ({
        label: key,
        kind: monaco.languages.CompletionItemKind.Constant,
        insertText: key,
      }));
    },
    updateReadonlyLines() {
      if (!this.readonlyLineContents || !this.readonlyLineContents.length) {
        this.currentReadonlyLines = [];
        return;
      }
      const lines = this.monacoEditor.getValue().split('\n');
      this.currentReadonlyLines = this.readonlyLineContents
        .map((content) =>
          lines.findIndex((line) => line.trim() === content.trim())
        )
        .filter((idx) => idx !== -1);
    },
    updateReadonlyDecorations() {
      const model = this.monacoEditor.getModel();
      const lineCount = model.getLineCount();
      const decorations = this.currentReadonlyLines
        .filter((line) => line + 1 <= lineCount)
        .map((line) => {
          const lineNumber = line + 1;
          const maxColumn = model.getLineMaxColumn(lineNumber);
          return {
            range: new monaco.Range(lineNumber, 1, lineNumber, maxColumn),
            options: {
              isWholeLine: true,
              className: 'readonly-line-highlight',
            },
          };
        });
      this.monacoEditor.deltaDecorations([], decorations);
    },
  },
  beforeDestroy() {
    if (this.monacoEditor) {
      this.monacoEditor.dispose();
    }
  },
};
</script>

<style lang="scss" scoped>
/* 高亮只读行 */
.readonly-line-highlight {
  background: #f8d7da !important;
}
</style>
