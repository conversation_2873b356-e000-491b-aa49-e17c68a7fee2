import service from '../base';
import config from '../config';
export default {
  // 获取基础层级指标列表
  getIndicatorList(planId) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/getBaseLevelIndicators`,
      method: 'get',
      params: { planId },
    });
  },
  // 新增基础层级指标
  addIndicator(params) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/editBaseLevelIndicator`,
      method: 'post',
      data: params,
    });
  },
  // 获取唯一编码
  getUniqueCode(planId) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/getCode`,
      method: 'get',
      params: { planId },
    });
  },
  // 删除基础层级指标
  deleteIndicator(id) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/delBaseLevelIndicators`,
      method: 'get',
      params: { id },
    });
  },
  // 获取指标体系列表
  getIndicatorSystemList(planId) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/getIndicatorSystemList`,
      method: 'get',
      params: { planId },
    });
  },
  // 新增指标体系
  addIndicatorSystem(data) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/editIndicatorSystem`,
      method: 'post',
      data,
    });
  },
  // 根据积分类型查询可选择学院集合
  getAcademyListByScoreType(params) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/getCollegeByPlanType`,
      method: 'get',
      params,
    });
  },
  // 删除指标体系
  deleteIndicatorSystem(id) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/delIndicatorSystem`,
      method: 'get',
      params: { id },
    });
  },
  // 获取指标层级列表
  getIndicatorLevelList(params) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/getIndicatorLevelList`,
      method: 'get',
      params,
    });
  },
  // 新增/编辑指标层级
  addIndicatorLevel(data) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/editIndicatorLevel`,
      method: 'post',
      data,
    });
  },
  // 删除指标层级
  deleteIndicatorLevel(id) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/delIndicatorLevel`,
      method: 'get',
      params: { id },
    });
  },
  // 获取指标列表
  getIndicatorListByLevel(params) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/getIndicatorList`,
      method: 'post',
      data: params,
    });
  },
  // 查询上级指标列表
  getParentIndicatorList(params) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/getParentIndicatorList`,
      method: 'get',
      params,
    });
  },
  // 新增/编辑指标
  editIndicator(data) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/editIndicator`,
      method: 'post',
      data,
    });
  },
  // 删除指标
  deleteIndicatorById(id) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/delIndicator`,
      method: 'get',
      params: { id },
    });
  },
  // 批量修改指标层级排序
  batchEditIndicatorLevelSort(data) {
    return service({
      url: `${config.VUE_MODULE_JMDX}/assessmentPlan/batchEditIndicatorLevelSort`,
      method: 'post',
      data,
    });
  },
};
