import service from './base';
import config from './config';

const baseApi = {
  // 获取用户信息 (获取当前登录用户信息时不传id参数)
  getUserInfo(id = null) {
    return service({
      url:
        config.VUE_MODULE_UPMS +
        'adm/security/user/info' +
        (id ? '/' + id : ''),
      method: 'get',
    });
  },
};
let Api = { ...baseApi };

//  网络请求方法声明 - 处理方法
const setApi = (parent, pathArr, index, apiData) => {
  let _pathStr = pathArr[index];
  if (_pathStr.indexOf('.js') !== -1) {
    parent[_pathStr.replace('.js', '')] = apiData;
  } else {
    if (!parent[_pathStr]) {
      parent[_pathStr] = {};
    }
    setApi(parent[_pathStr], pathArr, index + 1, apiData);
  }
};

// 网络请求方法配置
const apiPathArr = require.context('./api', true, /\.js$/);
apiPathArr.keys().forEach((path) => {
  const match = path.split('/');
  match && setApi(Api, match, 1, apiPathArr(path).default);
});

export default Api;
