import Vue from 'vue';
import config from './config';
// import testData from './testData/index';
// import { Message } from 'element-ui'

/*
 * 接口响应拦截器 - 检查校验
 *
 * 脚手架已内置统一异常code码的处理逻辑（比如 “400002：token失效” 等）
 * 此方法主要用于子项目定制化拦截处理接口返回内容，可根据实际项目业务进行添加
 */
const inspect = () => {
  return new Promise((resovle) => {
    // 示例
    // console.log(response)
    // const res = response.data
    // if(res.code !== 200) {
    //     if(res.code === 123456789) {
    //         console.log('xxxxx异常')
    //         // 异常信息通知提示（自行拦截的错误脚手架不展示通知提示，需自行实现）
    //         Message.error(res.message || '网络异常，请稍后再试')
    //     }
    // } else {
    //     console.log('正常')
    //     // doSomeThing
    //     // ...
    // }

    // 通过resovle回到网络底层模块主流程
    // showMessage：主流程是否弹出异常信息提示(仅在code!==200时生效，通常配置为true)
    resovle({ showMessage: true });
  });
};

const service = (option, cmd) => {
  return new Promise((resovle, reject) => {
    if (
      config.IS_TESR &&
      option.url.indexOf(config.VUE_MODULE_UPMS) === -1 &&
      cmd
    ) {
      setTimeout(() => {
        resovle();
        // typeof testData[cmd] === 'function'
        //   ? testData[cmd](option.data || option.params)
        //   : testData[cmd]
      }, config.TIME);
    } else {
      Vue.prototype
        .$service(option, inspect)
        .then((res) => resovle(res))
        .catch((error) => reject(error));
    }
  });
};

export default service;
