<template>
  <el-table
    ref="mainTable"
    :data="tableData"
    style="width: 100%"
    height="400"
    border
    :header-cell-style="{ background: '#fafafa' }"
  >
    <el-table-column
      v-for="(col, idx) in columns"
      :key="col.prop"
      :prop="col.prop"
      :label="col.label"
      :width="col.width"
      :fixed="idx > fixedCount ? 'right' : false"
      show-overflow-tooltip
    />
    <el-table-column label="操作" fixed="right" width="120">
      <template slot-scope="scope">
        <table-actions
          :row="scope.row"
          @detail="$emit('detail', scope.row)"
          @assign="$emit('assign', scope.row)"
          @revoke="$emit('revoke', scope.row)"
        />
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import TableActions from './TableActions.vue';
export default {
  name: 'DataTable',
  components: { TableActions },
  props: {
    columns: Array,
    tableData: Array,
    fixedCount: Number,
    confirmed: Boolean,
  },
  data() {
    return {
      mainTable: null,
    };
  },
  watch: {
    confirmed() {
      this.$nextTick(() => {
        if (this.$refs.mainTable && this.$refs.mainTable.doLayout) {
          this.$refs.mainTable.doLayout();
        }
      });
    },
  },
};
</script>

<style scoped>
.el-table {
  overflow-x: auto;
}
</style>
