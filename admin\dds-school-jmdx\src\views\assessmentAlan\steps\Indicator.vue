<template>
  <div>
    <div class="base-indicator-manage">
      <div class="base-indicator-header">
        <ItemLine title="基础层级指标管理"></ItemLine>
      </div>
      <div class="base-indicator-btns">
        <div
          v-for="item in baseIndicators"
          :key="item.id"
          class="indicator-btn-wrapper"
        >
          <el-button @click="handleEditBaseIndicator(item)">
            {{ item.name }}
          </el-button>
        </div>
        <el-button
          @click="openCreateModal"
          size="mini"
          icon="el-icon-plus"
        ></el-button>
      </div>
    </div>
    <div class="indicator-list-manage">
      <!-- <IndicatorCreator :baseIndicatorsList="baseIndicators" /> -->
      <IndicatorList
        ref="list"
        :baseIndicatorsList="baseIndicators"
        @success="handleSuccess"
      />
    </div>
    <div class="form-actions">
      <el-button class="custom-btn" type="primary" @click="handlePreviousStep"
        >上一步</el-button
      >
      <el-button class="custom-btn" type="primary" @click="handleNextStep"
        >下一步</el-button
      >
    </div>
    <IndicatorModal
      v-if="modalVisible"
      :visible.sync="modalVisible"
      :title="isEditModal ? '编辑基础指标' : '创建基础指标'"
      :showDelete="
        isEditModal && !!editModalData && editModalData.canDelete === 1
      "
      :editData="isEditModal ? editModalData : null"
      @created="handleBaseIndicatorModalOk"
      @deleted="handleRefreshAfterDelete"
    />
  </div>
</template>

<script>
import IndicatorList from './../components/IndicatorList.vue';
import ItemLine from '@/components/ItemLine.vue';
import IndicatorModal from './../components/IndicatorModal.vue';
import Request from '@/service';
export default {
  name: 'Indicator',
  components: { IndicatorList, ItemLine, IndicatorModal },
  data() {
    return {
      showModal: false,
      baseIndicators: [],
      editModalVisible: false,
      editModalData: null,
      deleteTarget: null,
      planId: this.$route.query.planId,
      modalVisible: false,
      isEditModal: false,
    };
  },
  created() {
    this.getBaseIndicators();
  },
  methods: {
    handleSuccess(val) {
      if (val) {
        this.getBaseIndicators();
      }
    },
    async getBaseIndicators() {
      try {
        const res = await Request.indicator.getIndicatorList(this.planId);
        if (res.code === 200 && res.data && Array.isArray(res.data)) {
          this.baseIndicators = res.data;
        }
      } catch (err) {
        console.log(err);
      }
    },
    handleNextStep() {
      this.$router.push({
        path: '/jmdx/assessment/step/rule',
        query: { planId: this.$route.query.planId },
      });
    },
    handlePreviousStep() {
      this.$router.push({
        path: '/jmdx/assessment/step/object',
        query: { planId: this.$route.query.planId },
      });
    },
    handleEditBaseIndicator(item) {
      this.editModalData = { ...item };
      this.isEditModal = true;
      this.modalVisible = true;
    },
    openCreateModal() {
      this.isEditModal = false;
      this.editModalData = null;
      this.modalVisible = true;
    },
    // 处理新增/编辑基础层级
    async handleBaseIndicatorModalOk(form) {
      // 判断是新增还是编辑
      console.log(form, 'form');
      const isEdit =
        !!form.id && this.baseIndicators.some((item) => item.id === form.id);
      // 保存基础层级
      try {
        let res = await Request.indicator.addIndicator({
          id: form.id || '',
          code: form.code,
          name: form.name,
          subject: form.subject,
          planId: Number(this.$route.query.planId),
        });
        if (res.code === 200) {
          // 刷新列表
          this.getBaseIndicators();
          this.modalVisible = false;
          this.isEditModal = false;
          this.$message.success(isEdit ? '编辑成功' : '新增成功');
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleRefreshAfterDelete() {
      this.getBaseIndicators();
      this.modalVisible = false;
    },
  },
};
</script>

<style scoped>
.base-indicator-manage {
  margin-bottom: 16px;
}
.base-indicator-header .desc {
  color: #f56c6c;
  font-size: 13px;
}
.base-indicator-btns {
  margin: 8px 0;
}
.indicator-list-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.indicator-list-header .desc {
  font-weight: bold;
  margin-right: 16px;
}
.indicator-list-header .help {
  color: #f56c6c;
  font-size: 13px;
}
.form-actions {
  text-align: center;
  margin-top: 30px;
}
.form-actions .custom-btn {
  width: 120px;
  border-radius: 15px;
}
.indicator-btn-wrapper {
  display: inline-block;
  position: relative;
  margin: 4px 8px 4px 0;
}
</style>
