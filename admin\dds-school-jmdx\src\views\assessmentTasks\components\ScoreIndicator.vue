<template>
  <div class="score-indicator-box">
    <div class="score-row">
      <div class="score-label">指标得分</div>
      <div class="score-value-wrap">
        <div class="score-value">
          {{ score }}
          <el-tooltip content="得分为生效数据累计得分" placement="top">
            <i class="el-icon-question score-icon"></i>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div class="indicator-row">
      <div class="score-label">拆解指标</div>
      <div class="indicator-list-wrap">
        <div class="indicator-list">
          <div v-for="(item, idx) in indicators" :key="item.name" :class="['indicator-item', { 'with-border': idx !== 0 }]">
            <div class="indicator-name">{{ item.name }}</div>
            <div class="indicator-value">{{ item.value }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScoreIndicator',
  props: {
    score: Number,
    indicators: Array
  }
}
</script>

<style scoped>
.score-indicator-box {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px #f0f1f2;
  padding: 0;
  margin-bottom: 16px;
  border: 1px solid #f1f1f1;
}
.score-row {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  min-width: 900px;
}
.score-label {
  width: 120px;
  font-weight: bold;
  font-size: 16px;
  padding: 16px 12px;
  background: #fafafa;
  white-space: nowrap;
  text-align: center;
  align-content: center;
}
.score-value-wrap {
  flex: 1;
  overflow-x: auto;
}
.score-value {
  display: flex;
  min-width: 180px;
  align-items: center;
  font-size: 22px;
  font-weight: bold;
  color: #333;
  padding: 16px 12px;
  background: #fff;
  white-space: nowrap;
}
.score-icon {
  margin-left: 4px;
  color: #333;
}
.indicator-row {
  display: flex;
  min-width: 900px;
}
.indicator-list-wrap {
  flex: 1;
  overflow-x: auto;
}
.indicator-list {
  display: flex;
  min-width: 180px;
}
.indicator-item {
  min-width: 180px;
  padding: 16px 12px;
  background: #fff;
  text-align: center;
  white-space: nowrap;
  font-size: 14px;
  color: #333;
}
.indicator-item.with-border {
  border-left: 1px solid #e4e7ed;
}
.indicator-name {
  color: #666;
  margin-bottom: 6px;
}
.indicator-value {
  font-size: 18px;
  font-weight: bold;
}
</style> 