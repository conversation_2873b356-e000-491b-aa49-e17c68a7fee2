<template>
  <div class="confirm-header-content">
    <div class="confirm-title">
      {{ title }}
      <span class="confirm-date">{{ date }}</span>
    </div>
    <div class="confirm-header">
      <div class="header-main">
        <div class="confirm-subtitle">{{ subtitle }}</div>
        <div class="confirm-warning">{{ warning }}</div>
      </div>
      <el-button type="primary" @click="$emit('btnClick')">{{ btnText }}</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HeaderInfo',
  props: {
    title: String,
    date: String,
    subtitle: String,
    warning: String,
    btnText: String
  }
}
</script>

<style scoped>
.confirm-header-content {
  /* margin-bottom: 16px; */
}
.confirm-title {
  font-size: 20px;
  font-weight: bold;
}
.confirm-date {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}
.confirm-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 8px;
  padding: 16px 24px 0 24px;
  
}
.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 60%;
}
.confirm-subtitle {
  font-size: 16px;
  font-weight: bold;
}
.confirm-warning {
  color: #e6a23c;
  margin-left: 24px;
}
</style> 