import VueRouter from 'vue-router';
import Store from '../store';

// 路由数据
let routes = [];

// 路由数据配置
const routePathArr = require.context('./config', true, /\.js$/);
routePathArr
  .keys()
  .forEach((path) => (routes = [...routes, ...routePathArr(path).default]));

// 注册路由
const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes,
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
  document.title = to.meta.title || '数字桌面';
  if (to.path.indexOf(process.env.VUE_APP_BASE_NAME) !== -1) {
    const user = Store.state && Store.state.user;
    !user && (await Store.dispatch('getUserInfo'));
  }
  next();
});

export default router;
