<template>
  <WorkBenchLayout>
    <template #sidebar>
      <WorkbenchSidebar userType="dept" />
    </template>
    <template #main>
      <DeptWorkbenchMain />
    </template>
  </WorkBenchLayout>
</template>

<script>
import WorkBenchLayout from './components/WorkBenchLayout.vue';
import WorkbenchSidebar from './components/WorkbenchSidebar.vue';
import DeptWorkbenchMain from './components/DeptWorkbenchMain.vue';
// import DeptWorkbenchMain from './components/PersonWorkbenchMain.vue';

export default {
  name: 'DeptPage',
  components: {
    WorkBenchLayout,
    WorkbenchSidebar,
    DeptWorkbenchMain,
  },
};
</script>

<style scoped></style>
