<template>
  <div class="person-main">
    <!-- 问号图标 -->
    <!-- <div class="help-icon" @click="startGuide" ref="helpIcon">
      <i class="el-icon-question"></i>
    </div> -->

    <div class="assessment-plan-section">
      <div class="assessment-plan-title">考核方案</div>
      <div class="assessment-plan-list">
        <div
          class="assessment-plan-item"
          v-for="item in assessmentPlans"
          :key="item.id"
        >
          <div class="plan-header">
            <div class="plan-status"> 进行中 </div>
            <span class="plan-title" :ref="`planTitle${item.id}`">{{
              item.title
            }}</span>
          </div>
          <el-button
            style="float: right"
            size="mini"
            type="primary"
            @click="handleDetail(item)"
            >查看详情</el-button
          >
          <div class="plan-info">
            <div class="info-left">
              <span>考核总时间：{{ item.time }}</span>
            </div>
            <div class="info-right">
              <span>总得分：{{ item.totalScore }} | </span>
              <span>低分项：{{ item.lowScore }} | </span>
              <span>高分项：{{ item.highScore }} </span>
              <p> 确认进度：{{ item.progress }} </p>
            </div>
          </div>
          <div class="plan-info" style="margin-top: 16px">
            <div>
              <span>确认截止时间：{{ item.jzTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
        <!-- 消息组件 -->
    <MessageList
      :messages="messageData"
      :loading="loading"
      :show-pagination="true"
      :page-size="18"
      :list-height="tableHeight"

      @tab-change="handleTabChange"
      @filter-change="handleFilterChange"
      @search="handleSearch"
      @page-change="handlePageChange"
      @message-click="handleMessageClick"
      @view-detail="handleViewDetail"
    />

  </div>
</template>

<script>
import Shepherd from 'shepherd.js';
import 'shepherd.js/dist/css/shepherd.css';
import MessageList from './MessageList.vue';
export default {
  components: {
    MessageList
  },
  name: 'PersonWorkbenchMain',
  data () {
    return {
      activeTab: 'notice',
      tour: null,
      assessmentPlans: [
        {
          id: 1,
          title: '2024年度基础性个人指标',
          time: '2024-12-16 ~ 2024-12-26',
          jzTime: '2024-12-23 15:00:00',
          totalScore: 1200,
          lowScore: 10,
          highScore: 20,
          progress: '10/50'
        },
        {
          id: 2,
          title: '2024年度发展性个人考核',
          time: '2024-12-16 ~ 2024-12-26',
          jzTime: '2024-12-23 15:00:00',
          totalScore: 1200,
          lowScore: 10,
          highScore: 20,
          progress: '10/50',
          type: 'person'
        },
        {
          id: 3,
          title: '2024年度创新性个人考核',
          time: '2024-12-16 ~ 2024-12-26',
          jzTime: '2024-12-23 15:00:00',
          totalScore: 1200,
          lowScore: 10,
          highScore: 20,
          progress: '10/50',
          type: 'person'
        }
      ],
      tableData: [
        {
          id: 1,
          type: '催办',
          content: '考核确认催办通知',
          time: '2023-11-13 17:29:44',
          read: false,
          status: 'active'
        },
        {
          id: 2,
          type: '审核',
          content: '提交的关于教学成果指标的填报申请审核通过通知',
          time: '2023-11-13 17:29:44',
          read: true,
          status: 'active'
        },
        {
          id: 3,
          type: '审核',
          content: '2025年度积分考核,教学成果指标有新的数据填报申请',
          time: '2023-11-13 17:29:44',
          read: false,
          status: 'active'
        },
        {
          id: 4,
          type: '审核',
          content: '2025年度积分考核,教学成果指标有新的数据纠错申请',
          time: '2023-11-13 17:29:44',
          read: true,
          status: 'active'
        },
        {
          id: 5,
          type: '审核',
          content: '提交的关于教学成果指标的纠错申请审核通过通知',
          time: '2023-11-13 17:29:44',
          read: false,
          status: ''
        },
        {
          id: 6,
          type: '审核',
          content: '提交的关于教学成果指标的纠错申请审核被驳回',
          time: '2023-11-13 17:29:44',
          read: true,
          status: ''
        },
        {
          id: 7,
          type: '提醒',
          content: '2025年度积分考核还有24小时就截止了,请尽快确认',
          time: '2023-11-13 17:29:44',
          read: false,
          status: ''
        },
        {
          id: 8,
          type: '提醒',
          content: '提交的关于教学成果指标的纠错申请审核通过通知',
          time: '2023-11-13 17:29:44',
          read: true,
          status: ''
        }
      ],
      filterType: '',
      filterKeyword: '',
      filterRead: 'all', // 新增filterRead数据
      tableHeight: 'calc(100vh - 554px)',
         loading: false,
      messageData: [
        {
          id: 1,
          type: '催办',
          title: '考核确认催办通知',
          content: '您有一项考核需要确认，请及时处理',
          time: '2023-11-13 17:29:44',
          isRead: false
        },
        {
          id: 2,
          type: '审核',
          title: '提交的关于教学成果指标的填报申请审核通过通知',
          content: '您提交的教学成果指标填报申请已通过审核',
          time: '2023-11-13 17:29:44',
          isRead: false
        },
        {
          id: 3,
          type: '审核',
          title: '2025年度积分考核，教学成果指标有新的数据填报申请',
          content: '2025年度积分考核教学成果指标有新的数据需要填报',
          time: '2023-11-13 17:29:44',
          isRead: false
        },
        {
          id: 4,
          type: '审核',
          title: '2025年度积分考核，教学成果指标有新的数据填报申请',
          content: '2025年度积分考核教学成果指标有新的数据需要填报',
          time: '2023-11-13 17:29:44',
          isRead: false
        },
        {
          id: 5,
          type: '审核',
          title: '提交的关于教学成果指标的出版申请审核被驳回',
          content: '您提交的教学成果指标出版申请未通过审核，请重新提交',
          time: '2023-11-13 17:29:44',
          isRead: false
        },
        {
          id: 6,
          type: '审核',
          title: '提交的关于教学成果指标的出版申请审核被驳回',
          content: '您提交的教学成果指标出版申请未通过审核，请重新提交',
          time: '2023-11-13 17:29:44',
          isRead: true
        },
        {
          id: 7,
          type: '提醒',
          title: '2025年度积分考核还有24小时就截止了，请尽快确认',
          content: '2025年度积分考核即将截止，请及时完成相关操作',
          time: '2023-11-13 17:29:44',
          isRead: true
        },
        {
          id: 8,
          type: '提醒',
          title: '提交的关于教学成果指标的出版申请审核通过通知',
          content: '您提交的教学成果指标出版申请已通过审核',
          time: '2023-11-13 17:29:44',
          isRead: true
        },
        {
          id: 9,
          type: '通知',
          title: '系统维护通知',
          content: '系统将于今晚进行维护，请提前保存工作',
          time: '2023-11-12 15:30:00',
          isRead: true
        },
        {
          id: 10,
          type: '催办',
          title: '年度考核材料提交催办',
          content: '请尽快提交年度考核相关材料',
          time: '2023-11-12 10:15:30',
          isRead: false
        },
        {
          id: 8,
          type: '提醒',
          title: '提交的关于教学成果指标的出版申请审核通过通知',
          content: '您提交的教学成果指标出版申请已通过审核',
          time: '2023-11-13 17:29:44',
          isRead: true
        },
        {
          id: 9,
          type: '通知',
          title: '系统维护通知',
          content: '系统将于今晚进行维护，请提前保存工作',
          time: '2023-11-12 15:30:00',
          isRead: true
        },
        {
          id: 10,
          type: '催办',
          title: '年度考核材料提交催办',
          content: '请尽快提交年度考核相关材料',
          time: '2023-11-12 10:15:30',
          isRead: false
        }
      ]
    };
  },
  computed: {
    filteredTableData () {
      return this.tableData.filter(item => {
        const typeMatch = this.filterType
          ? item.type === this.filterType
          : true;
        const keywordMatch = this.filterKeyword
          ? item.content.includes(this.filterKeyword)
          : true;
        // 假设item.read为true表示已读，false表示未读
        let readMatch = true;
        if (this.filterRead === 'unread') readMatch = item.read === false;
        if (this.filterRead === 'read') readMatch = item.read === true;
        return typeMatch && keywordMatch && readMatch;
      });
    }
  },
  mounted () {
    this.initTour();
    // 开发环境下自动启动引导（可选，用于测试）
    // this.$nextTick(() => {
    //   setTimeout(() => {
    //     this.startGuide();
    //   }, 1000);
    // });
  },
  beforeDestroy () {
    if (this.tour && this.tour.destroy) {
      this.tour.destroy();
    }
  },
  methods: {
    handleDetail (data) {
      console.log(data);
      this.$router.push({
        name: 'AssessmentDetail',
        query: {
          id: data.id,
          name: data.title,
          type: data.type === 'person' ? '个人考核' : '教师考核'
        }
      });
    },
    handleView (row) {
      console.log(row);
    },
    handleMore () {
      // 跳转或弹窗逻辑
      this.$message.info('更多功能待实现');
    },
    handleReadChange () {
      // 可选：切换时做其他处理
    },

    // 获取状态标签类型
    getStatusType (type) {
      switch (type) {
        case '催办':
          return 'warning';
        case '审核':
          return 'primary';
        case '提醒':
          return 'info';
        default:
          return 'info';
      }
    },

    // 初始化新手引导
    initTour () {
      this.tour = new Shepherd.Tour({
        defaultStepOptions: {
          cancelIcon: {
            enabled: true
          },
          classes: 'shepherd-theme-arrows',
          scrollTo: true
        },
        useModalOverlay: true
      });

      // 步骤1：欢迎页面
      this.tour.addStep({
        id: 'welcome',
        title: '',
        text: '你好！\n现在让我带你了解下,积分改革系统的基本功能区域吧',
        buttons: [
          {
            text: '不用了',
            action: () => {
              this.tour.complete();
            },
            classes: 'shepherd-button-secondary'
          },
          {
            text: '开始吧',
            action: () => {
              this.tour.next();
            },
            classes: 'shepherd-button-primary'
          }
        ],
        classes: 'shepherd-welcome-step'
        // attachTo: {
        //   element: '.workbench-layout',
        //   on: 'center'
        // }
      });

      // 步骤2：考核方案区域
      this.tour.addStep({
        id: 'assessment-plan-section',
        title: '考核方案',
        text: '您可以点击待办、消息、公告按钮,查看最新的事务及通告内容;同时,可以点击更多,查看全部的记录。',
        buttons: [
          {
            text: '上一步',
            action: () => {
              this.tour.back();
            },
            classes: 'shepherd-button-secondary'
          },
          {
            text: '下一步',
            action: () => {
              this.tour.next();
            },
            classes: 'shepherd-button-primary'
          }
        ],
        attachTo: {
          element: '.assessment-plan-section',
          on: 'bottom'
        },
        highlightClass: 'shepherd-highlight'
      });

      // 步骤3：快捷入口区域
      this.tour.addStep({
        id: 'sidebar-quick',
        title: '快捷入口',
        text: '此处展示当前身份可使用的功能入口，包括：考核任务中心、考核过程管理、考核结果查看；若入口缺失，可联系管理员进行授权。',
        buttons: [
          {
            text: '上一步',
            action: () => {
              this.tour.show('assessment-plan-section');
            },
            classes: 'shepherd-button-secondary'
          },
          {
            text: '下一步',
            action: () => {
              this.tour.next();
            },
            classes: 'shepherd-button-primary'
          }
        ],
        attachTo: {
          element: '.sidebar-quick',
          on: 'right'
        },
        highlightClass: 'shepherd-highlight'
      });

      // 步骤4：消息提醒区域
      this.tour.addStep({
        id: 'message-reminder',
        title: '消息提醒',
        text: '您可以点击待办、消息、公告按钮,查看最新的事务及通告内容;同时,可以点击更多,查看全部的记录。',
        buttons: [
          {
            text: '上一步',
            action: () => {
              this.tour.show('second-plan-title');
            },
            classes: 'shepherd-button-secondary'
          },
          {
            text: '结束指引',
            action: () => {
              this.tour.complete();
            },
            classes: 'shepherd-button-primary'
          }
        ],
        attachTo: {
          element: '.main-tabs',
          on: 'left'
        },
        highlightClass: 'shepherd-highlight'
      });
    },

    // 开始新手引导
    startGuide () {
      if (this.tour) {
        this.tour.start();
      }
    }
  }
};
</script>

<style lang="scss">
.person-main {
  width: 100%;
  height: 100%;
  position: relative;
}

// 问号图标样式
.help-icon {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  z-index: 1000;
  transition: all 0.3s ease;

  &:hover {
    background: #337ecc;
    transform: scale(1.1);
  }

  i {
    color: white;
    font-size: 20px;
  }
}
.shepherd-header {
  background: #fff;
  color: white;
  border-radius: 8px 8px 0 0;
  padding: 12px 16px;
}
// Shepherd.js 样式自定义
.shepherd-theme-arrows {
  .shepherd-content {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .shepherd-header {
    background: #fff;
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 12px 16px;
  }

  .shepherd-title {
    font-size: 16px;
    font-weight: 600;
  }

  .shepherd-text {
    padding: 16px;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
  }

  .shepherd-footer {
    width: 100%;
    background: #fff;
    padding: 12px 16px;
    border-top: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
  }

  .shepherd-button {
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;

    &.shepherd-button-primary {
      background: #409eff;
      color: white;

      &:hover {
        background: #337ecc;
      }
    }

    &.shepherd-button-secondary {
      background: #f5f5f5;
      color: #666;
      border: 1px solid #d9d9d9;

      &:hover {
        background: #e6e6e6;
      }
    }
  }
}

.shepherd-highlight {
  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.3);
  border-radius: 4px;
}
.shepherd-has-title .shepherd-content .shepherd-header {
  background: #fff;
}
// 欢迎页面自定义样式
.shepherd-welcome-step {
  .shepherd-text {
    width: 100%;
    background: #fff;
    border-top: 1px solid #fff;
    position: relative;
    padding-top: 160px; // 为图片留出空间
    text-align: center;

    &::before {
      content: '';
      position: absolute;
      top: -45px;
      left: 50%;
      transform: translateX(-50%);
      width: 362px;
      height: 200px;
      background-image: url('~@/assets/img/zhiyin.png');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      border-radius: 12px;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
  }

  .shepherd-title {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin-bottom: 16px;
  }

  .shepherd-content {
    width: 450px;
    max-width: 90vw;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  }
}

.welcome-content {
  text-align: center;
  padding: 20px;
}

.welcome-image {
  margin-bottom: 20px;

  img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.welcome-text {
  h3 {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin: 0 0 12px 0;
  }

  p {
    font-size: 16px;
    line-height: 1.6;
    color: #666;
    margin: 0;
  }
}

.assessment-plan-section {
  padding: 20px 12px 20px 24px;
  background: #ffffff;
  border-radius: 8px;
  height: 272px;
  .assessment-plan-title {
    height: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #060607;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    margin-bottom: 16px;
  }
  .assessment-plan-list {
    max-height: 200px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding-right: 12px;
    &::-webkit-scrollbar {
      width: 6px;
      background: transparent;
    }
    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    .assessment-plan-item {
      height: 96px;
      background: #f7f8fa;
      border-radius: 4px;
      margin-bottom: 8px;
      padding: 16px;
      box-sizing: border-box;
      .plan-header {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        .plan-status {
          width: 52px;
          height: 20px;
          background: rgba(22, 119, 255, 0.1);
          border-radius: 2px;

          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #1677ff;
          line-height: 20px;
          text-align: center;
          font-style: normal;
        }
        .plan-title {
          height: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #060607;
          line-height: 14px;
          text-align: left;
          font-style: normal;
          margin-left: 8px;
        }
      }
      .plan-info {
        display: flex;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #979da6;
        line-height: 12px;
        text-align: left;
        font-style: normal;
        .info-left {
          height: 12px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #979da6;
          line-height: 12px;
          text-align: left;
          font-style: normal;
        }
        .info-right {
          margin-left: auto;
          margin-right: 16px;
          height: 12px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #2f3338;
          line-height: 12px;
          text-align: left;
          font-style: normal;
          margin-top: -12px;
          p {
            height: 12px;
            margin-top: 8px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #1677ff;
            line-height: 12px;
            text-align: right;
            font-style: normal;
          }
        }
      }
    }
  }
}

.main-tabs {
  padding: 16px 24px;
  position: relative;
  background: #fff;
  margin-top: 18px;
  border-radius: 8px;
}
/* 自定义表格滚动条样式优化 */
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.table-toolbar-right {
  display: flex;
  align-items: center;
  margin-left: auto;
}
.table-toolbar-more {
  margin-left: 16px;
  position: absolute;
  right: 28px;
  top: 28px;
}
.more-link {
  color: #8a2be2;
  cursor: pointer;
  font-size: 15px;
  font-weight: bold;
  user-select: none;
}
.read-filter-group {
  margin-right: 16px;
}

// 自定义表格样式
.custom-table {
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

// 表头样式
.table-header {
  display: flex;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;

  .header-cell {
    padding: 12px 16px;
    font-weight: 600;
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;

    &.status-cell {
      width: 120px;
      justify-content: center;
    }

    &.content-cell {
      flex: 1;
      min-width: 400px;
    }

    &.time-cell {
      width: 180px;
      justify-content: center;
    }

    &.action-cell {
      width: 100px;
      justify-content: center;
    }
  }
}

// 表格内容样式
.table-body {
  overflow-y: auto;

  .table-row {
    display: flex;
    border-bottom: 1px solid #ebeef5;
    transition: background-color 0.3s ease;

    &:hover {
      background: #f5f7fa;
    }

    &:last-child {
      border-bottom: none;
    }

    .table-cell {
      padding: 12px 16px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333;

      &.status-cell {
        width: 120px;
        justify-content: center;
      }

      &.content-cell {
        flex: 1;
        min-width: 400px;
      }

      &.time-cell {
        width: 180px;
        justify-content: center;
      }

      &.action-cell {
        width: 100px;
        justify-content: center;
      }
    }
  }
}

// 状态相关样式
.status-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 20px; // 确保容器有固定高度
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
  background: transparent; // 默认透明
  transition: background-color 0.3s ease;

  &.active {
    background: #67c23a; // 激活时显示绿色
  }
}

.status-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.content-text {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  text-align: left;
  word-break: break-all;
}

.action-btn {
  color: #333;
  font-size: 14px;
  padding: 4px 8px;

  &:hover {
    color: #409eff;
  }
}

// 滚动条样式
.table-body::-webkit-scrollbar {
  width: 6px;
  background: transparent;
}

.table-body::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.table-body::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 3px;
}

.table-body {
  scrollbar-width: thin;
  scrollbar-color: #c0c4cc #f5f7fa;
}
</style>
